<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
    <div
        v-if="Loading"
        v-loading="Loading"
        class="loadingBox"
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(widget1) // 这段代码不要删
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { pairInfo, isPairDetail, pair, ticker } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const { option, tradingviewLangMap } = useTradingView(colorMode.preference, 'green-up')
const tradingViewOption = option[colorMode.preference]
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: '15m'
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: true
  },
  // 专业版优化配置开关 - 不影响基本版功能
  enableProfessionalOptimization: {
    type: Boolean,
    default: true
  }
})
const resolutionReMap: any = {
  'line': 1,
  '1m': 1,
  '5m': 5,
  '15m': 15,
  '30m': 30,
  '1h': 60,
  '2h': 120,
  '4h': 240,
  '6h': 360,
  '8h': 480,
  '12h': 720,
  '1d': '1d',
  '1w': '1w',
  '1M': '1M'
}
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting'])
// 类型安全的widgetOption配置
const getSafeSymbol = () => {
  const symbol = props.pair || pair.value
  if (typeof symbol !== 'string' || !symbol) {
    console.warn('[TradingView] Invalid symbol, using default:', symbol)
    return 'ETH_USDT'
  }
  return symbol
}

const getSafeInterval = () => {
  const resolution = props.resolution
  if (!resolution || !resolutionReMap[resolution]) {
    console.warn('[TradingView] Invalid resolution, using default:', resolution)
    return '15'
  }
  return resolutionReMap[resolution]
}

const getSafeLocale = () => {
  const mappedLocale = tradingviewLangMap[locale.value]
  if (mappedLocale && typeof mappedLocale === 'string') {
    return mappedLocale
  }
  if (typeof locale.value === 'string') {
    return locale.value
  }
  return 'en'
}

const widgetOption = {
  debug: false,
  symbol: getSafeSymbol(),
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction(pairInfo.value),
  interval: getSafeInterval(),
  locale: getSafeLocale(),
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...tradingViewOption
} as any
let widget: any = null
let datafeedInstance: any = null
const Loading = ref(true)
const isRebuilding = ref(false) // 防止重复重建的状态标记
const isResolutionChanging = ref(false) // 专业版周期切换状态锁
const resolutionChangeTimeout = ref<NodeJS.Timeout | null>(null) // 防抖延迟
const kline_container = ref<HTMLElement>() // kline容器ref引用
const initRetryCount = ref(0) // 初始化重试计数器
const MAX_INIT_RETRIES = 10 // 最大重试次数

const initChart = async () => {
  const currentPair = props.pair || pair.value

  if (!currentPair) {
    return
  }
  
  // 使用nextTick确保DOM已渲染
  await nextTick()
  
  // 优先使用ref引用，fallback到getElementById
  let container = kline_container.value
  if (!container) {
    container = document.getElementById('kline_container')
  }
  
  if (!container) {
    if (initRetryCount.value < MAX_INIT_RETRIES) {
      initRetryCount.value++
      setTimeout(() => initChart(), 100)
      return
    } else {
      initRetryCount.value = 0
      return
    }
  }
  
  // 重置重试计数器
  initRetryCount.value = 0

  // 专业版1日周期修复：初始化时清理可能污染的缓存
  const currentResolution = props.resolution
  if (currentResolution === '1d') {
    try {
      const store = commonStore()
      // 如果store中有1M的缓存数据，清理它以避免污染1日周期
      if (store.klineTicker.currentPeriod === '1M') {
        store.klineList = []
        store.klineTicker = {}
      }
    } catch (error) {
    }
  }

  if (props.resolution === '1M') {
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  datafeedInstance = useDatafeedAction(pairInfo.value, {
    enableProfessionalOptimization: props.enableProfessionalOptimization
  })
  
  // 专业版1日周期修复：如果是1日周期，强制清理datafeed缓存
  if (currentResolution === '1d' && datafeedInstance) {
    try {
      if (typeof datafeedInstance.clearCache === 'function') {
        datafeedInstance.clearCache(true)
      }
      if (typeof datafeedInstance.setForceRefresh === 'function') {
        datafeedInstance.setForceRefresh(true)
      }
    } catch (error) {
    }
  }
  
  widgetOption.datafeed = datafeedInstance
  widgetOption.symbol = currentPair
  widgetOption.interval = props.resolution ? resolutionReMap[props.resolution] : '15',
  widget = new window.TradingView.widget(widgetOption)
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1)
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
    Loading.value = false
  })
}

const previousResolution = ref(props.resolution)

// 防抖周期切换函数
const debouncedResolutionChange = (newVal: string, oldVal: string) => {
  // 清除之前的延迟
  if (resolutionChangeTimeout.value) {
    clearTimeout(resolutionChangeTimeout.value)
  }
  
  resolutionChangeTimeout.value = setTimeout(() => {
    handleResolutionChange(newVal, oldVal)
  }, 150) // 增加到800ms防抖延迟，确保在快速切换时不会重叠
}

const handleResolutionChange = (newVal: string, oldVal: string) => {
  // 防止在重建过程中重复触发
  if (isRebuilding.value) {
    console.warn('[TradingView] Widget is rebuilding, ignoring resolution change')
    return
  }
  
  if (!newVal || !widget || newVal === oldVal) {
    return
  }
  const isFromMonthly = oldVal === '1M'
  const isToMonthly = newVal === '1M'
  const isSignificantChange = isFromMonthly !== isToMonthly
  const isToDaily = newVal === '1d'
  
  // 专业版1日周期修复：清理可能污染的基本版缓存数据
  if (isToDaily && datafeedInstance) {
    try {
      // 清理datafeed缓存，避免基本版数据污染
      if (typeof datafeedInstance.clearCache === 'function') {
        datafeedInstance.clearCache(true)
      }
      // 清理store中可能冲突的1M相关状态
      const store = commonStore()
      if (store.klineTicker.currentPeriod === '1M') {
        store.klineList = []
        store.klineTicker = {}
      }
    } catch (error) {
    }
  }
  
  // 在特殊情况下通知datafeed将有周期切换
  if (datafeedInstance && typeof datafeedInstance.setForceRefresh === 'function' && (isSignificantChange || isToDaily)) {
    try {
      datafeedInstance.setForceRefresh(true)
    } catch (error) {
      console.warn('[TradingView] Failed to set force refresh:', error)
    }
  }
  
  // 专业版优化：使用TradingView官方推荐的重置机制
  if (props.enableProfessionalOptimization) {
    // 防止并发的周期切换
    if (isResolutionChanging.value) {
      return
    }
    
    isResolutionChanging.value = true
    
    if (datafeedInstance) {
      try {
        // 1. 设置周期切换状态，阻止实时数据推送
        if (datafeedInstance.isResolutionChanging) {
          datafeedInstance.isResolutionChanging.value = true
        }
        
        // 2. 使用官方推荐的重置机制
        if (typeof datafeedInstance.triggerChartReset === 'function') {
          datafeedInstance.triggerChartReset(props.pair || pair.value, newVal)
        }
        
        // 3. 清理状态和取消请求
        if (typeof datafeedInstance.clearCache === 'function') {
          datafeedInstance.clearCache(true)
        }
        if (typeof datafeedInstance.cancelAllActiveRequests === 'function') {
          datafeedInstance.cancelAllActiveRequests()
        }
      } catch (error) {
        console.warn('[TradingView Professional] Failed to clean datafeed state:', error)
      }
    }
    
    try {
      widget.activeChart().setResolution(resolutionReMap[newVal])
      
      // 延迟恢复实时数据推送
      setTimeout(() => {
        try {
          // 验证新订阅已建立
          const subscriptionKey = `${props.pair || pair.value}_#_${resolutionReMap[newVal]}`
          const subscription = datafeedInstance && datafeedInstance.subMap && datafeedInstance.subMap[subscriptionKey]
          
          if (subscription || !datafeedInstance) {
            // 只有在新订阅已建立或datafeed不存在时才恢复状态
            if (datafeedInstance && datafeedInstance.isResolutionChanging) {
              datafeedInstance.isResolutionChanging.value = false
            }
            isResolutionChanging.value = false
          } else {
            // 如果订阅还未建立，再等待一段时间
            setTimeout(() => {
              if (datafeedInstance && datafeedInstance.isResolutionChanging) {
                datafeedInstance.isResolutionChanging.value = false
              }
              isResolutionChanging.value = false
            }, 300)
          }
        } catch (error) {
          console.warn('[TradingView Professional] Failed to restore state:', error)
          isResolutionChanging.value = false
        }
      }, 500) // 增加延迟到500ms，确保新订阅完全建立
      
      return
    } catch (error) {
      isResolutionChanging.value = false
      if (datafeedInstance && datafeedInstance.isResolutionChanging) {
        datafeedInstance.isResolutionChanging.value = false
      }
      rebuildWidget(newVal)
      return
    }
  }
  
  // 优化1日周期处理：优先使用原生切换，避免不必要的重建
  if (isToDaily) {
    try {
      // 尝试使用TradingView原生方法切换，避免重建导致的重复接口调用
      widget.activeChart().setResolution(resolutionReMap[newVal])
      return
    } catch (error) {
      rebuildWidget(newVal)
      return
    }
  }
  
  // 优先使用TradingView原生切换
  try {
    widget.activeChart().setResolution(resolutionReMap[newVal])
  } catch (error) {
    rebuildWidget(newVal)
  }
}

const rebuildWidget = (newVal: string) => {
  if (isRebuilding.value || isInitializing.value) {
    return
  }
  
  isRebuilding.value = true
  // 增强的清理超时机制：确保重建状态不会被永久锁定
  const rebuildTimeout = setTimeout(() => {
    if (isRebuilding.value) {
      console.warn('[TradingView] Rebuild timeout, forcing reset')
      isRebuilding.value = false
      Loading.value = false
    }
  }, 15000) // 15秒超时
  
  try {
    // 专业版1日周期修复：重建前的额外清理
    if (newVal === '1d') {
      try {
        const store = commonStore()
        // 清理可能冲突的基本版缓存
        if (store.klineTicker.currentPeriod === '1M') {
          store.klineList = []
          store.klineTicker = {}
        }
      } catch (error) {
      }
    }
    
    // 取消活跃请求并清理旧的datafeed缓存
    if (datafeedInstance) {
      try {
        if (typeof datafeedInstance.cancelAllActiveRequests === 'function') {
          datafeedInstance.cancelAllActiveRequests()
        }
        if (typeof datafeedInstance.clearCache === 'function') {
          datafeedInstance.clearCache(true)
        }
      } catch (error) {
        console.warn('[TradingView] Failed to clear cache:', error)
      }
    }
    
    // 移除旧widget
    if (widget) {
      try {
        widget.remove()
        widget = null
      } catch (error) {
        console.warn('[TradingView] Error removing old widget:', error)
        widget = null // 强制清空
      }
    }
    
    // 重建 widget
    nextTick(() => {
      try {
        clearTimeout(rebuildTimeout) // 清除超时定时器
        Loading.value = true
        datafeedInstance = useDatafeedAction(pairInfo.value, {
          enableProfessionalOptimization: props.enableProfessionalOptimization
        })
        
        // 专业版1日周期修复：重建时的特殊处理
        if (newVal === '1d' && datafeedInstance) {
          try {
            if (typeof datafeedInstance.setForceRefresh === 'function') {
              datafeedInstance.setForceRefresh(true)
            }
          } catch (error) {
            console.warn('[TradingView] Failed to set force refresh during rebuild:', error)
          }
        }
        
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = props.pair || pair.value
        widgetOption.interval = resolutionReMap[newVal]
        widget = new window.TradingView.widget(widgetOption)
        
        widget.onChartReady(() => {
          try {
            widget.activeChart().setChartType(1)
            widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
            Loading.value = false
            isRebuilding.value = false
          } catch (error) {
            isRebuilding.value = false
            Loading.value = false
          }
        })
      } catch (error) {
        isRebuilding.value = false
        Loading.value = false
        clearTimeout(rebuildTimeout)
      }
    })
  } catch (error) {
    isRebuilding.value = false
    Loading.value = false
    clearTimeout(rebuildTimeout)
  }
}

watch(() => props.resolution, (newVal, oldVal) => {
  if (newVal && widget && newVal !== oldVal) {
    debouncedResolutionChange(newVal, oldVal)
  }
  previousResolution.value = newVal
})
watch(() => pair.value, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget && !props.isLoading) {
    widget.activeChart().setSymbol(newVal)
  }
})

// 监听 props.pair 的变化
watch(() => props.pair, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget && !props.isLoading) {
    widget.activeChart().setSymbol(newVal)
  }
})
const hasChanged = ref(false)
const isInitializing = ref(false) // 防止重复初始化标记

onMounted(() => {
  // 组件挂载时，检查是否有 pair 参数和 pairInfo 数据
  const currentPair = props.pair || pair.value
  
  if (currentPair && JSON.stringify(pairInfo.value) !== '{}' && !isInitializing.value) {
    isInitializing.value = true
    initChart().finally(() => {
      isInitializing.value = false
    })
    hasChanged.value = true
  }
})

// 分别监听 props.pair 和 pairInfo 的变化
watch(() => props.pair, (newPair, oldPair) => {
  const currentPair = newPair || pair.value
  
  if (!hasChanged.value && currentPair && JSON.stringify(pairInfo.value || {}) !== '{}' && !isInitializing.value) {
    hasChanged.value = true
    isInitializing.value = true
    initChart().finally(() => {
      isInitializing.value = false
    })
  }
}, { immediate: true })

watch(() => pairInfo.value, (newPairInfo, oldPairInfo) => {
  const currentPair = props.pair || pair.value
  
  if (!hasChanged.value && currentPair && JSON.stringify(newPairInfo || {}) !== '{}' && !isInitializing.value) {
    hasChanged.value = true
    isInitializing.value = true
    initChart().finally(() => {
      isInitializing.value = false
    })
  }
}, { immediate: true })

watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => props.isShowTechnicalIndicator, (TechnicalIndicator) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  }
}, { immediate: true })

watch(() => props.isShowTradingViewSetting, (Setting) => {
  if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
}, { immediate: true })

watch(() => ticker.value[props.pair || pair.value], (currentTicker, previousTicker) => {
  const targetPair = props.pair || pair.value
  
  // 增强的专业版价格更新机制：双重保障确保价格同步
  if (currentTicker && widget && !props.isLoading && !isRebuilding.value) {
    
    // 检查是否有实际的价格数据变化
    const hasPriceChange = !previousTicker || 
                          previousTicker.last !== currentTicker.last ||
                          previousTicker.high !== currentTicker.high ||
                          previousTicker.low !== currentTicker.low
    
    // 检查是否有更新时间戳变化，用于验证数据新鲜度
    const hasUpdateChange = !previousTicker || 
                           previousTicker._lastUpdate !== currentTicker._lastUpdate
    
    
    if ((hasPriceChange || hasUpdateChange) && currentTicker.last) {
      try {
        // 双重保障机制：
        // 1. 依赖datafeed的subscription机制传递数据（主要方式）
        // 2. 轻量级刷新作为备用触发机制
        
        // 方法1：轻量级刷新 - 触发图表重绘，配合datafeed数据更新
        if (widget.activeChart && typeof widget.activeChart === 'function') {
          const chart = widget.activeChart()
          
          // 使用TradingView的原生方法进行轻量级更新
          if (chart && typeof chart.refreshChart === 'function') {
            chart.refreshChart()
          } 
        }
        
      } catch (error) {
      }
    }
  }
}, { immediate: false, deep: true })

// 组件销毁时清理资源
onBeforeUnmount(() => {
  // 清除延迟定时器
  if (resolutionChangeTimeout.value) {
    clearTimeout(resolutionChangeTimeout.value)
    resolutionChangeTimeout.value = null
  }
  
  // 清理widget
  if (widget) {
    try {
      widget.remove()
    } catch (error) {}
    widget = null
  }
  
  // 取消所有活跃请求并清理datafeed缓存
  if (datafeedInstance) {
    try {
      if (typeof datafeedInstance.cancelAllActiveRequests === 'function') {
        datafeedInstance.cancelAllActiveRequests()
      }
      if (typeof datafeedInstance.clearCache === 'function') {
        datafeedInstance.clearCache(false)
      }
    } catch (error) {
      console.warn('[TradingView] Error cleaning up datafeed:', error)
    }
  }
  
  // 重置状态
  isRebuilding.value = false
  isResolutionChanging.value = false
  Loading.value = true
})
</script>
<style lang="scss" scoped>  
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>